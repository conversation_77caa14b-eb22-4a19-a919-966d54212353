import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { RealViewport } from "@/components/layout/real-viewport";
import { GSAP } from "@/components/gsap/gsap";
import { ReactTempus } from "tempus/react";
import { Footer } from "@/components/layout/footer";

const sans = Montserrat({
	variable: "--font-sans",
	subsets: ["latin"],
});

export const metadata: Metadata = {
	title: "Create Next App",
	description: "Generated by create next app",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html
			lang="de"
			dir="ltr"
			className={`${sans.variable}`}
			suppressHydrationWarning
		>
			<body className={"antialiased"}>
				<RealViewport />
				{children}
				<Footer />
				<GSAP />
				<ReactTempus patch />
			</body>
		</html>
	);
}
