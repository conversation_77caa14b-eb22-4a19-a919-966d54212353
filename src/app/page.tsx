"use client";

import { Benefits } from "@/components/sections/benefits";
import { <PERSON> } from "@/components/sections/hero";
import { Intro } from "@/components/sections/intro";
import { Podcasts } from "@/components/sections/podcasts";
import { Services } from "@/components/sections/services";
import ReactLenis from "lenis/react";
import type { NextPage } from "next";

const IndexPage: NextPage = () => {
	return (
		<ReactLenis root>
			<Hero />
			<Intro />
			<Services />
			<Benefits />
			<Podcasts />
		</ReactLenis>
	);
};

export default IndexPage;
