"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const [activeIndex, setActiveIndex] = useState<number | null>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const cards = gsap.utils.toArray<HTMLElement>(".benefit-card");
			const images = gsap.utils.toArray<HTMLElement>(".benefit-image");
			const numbers = gsap.utils.toArray<HTMLElement>(".benefit-number");
			const lines = gsap.utils.toArray<HTMLElement>(".benefit-line");

			// Initial setup
			gsap.set(cards, {
				y: 60,
				opacity: 0,
			});

			gsap.set(images, {
				scale: 1.2,
				opacity: 0,
			});

			gsap.set(numbers, {
				y: 30,
				opacity: 0,
			});

			gsap.set(lines, {
				scaleX: 0,
				transformOrigin: "left center",
			});

			// Main timeline
			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top 70%",
					end: "bottom 30%",
					toggleActions: "play none none reverse",
				},
			});

			// Animate cards in sequence
			tl.to(cards, {
				y: 0,
				opacity: 1,
				duration: 1,
				stagger: 0.15,
				ease: "power3.out",
			})
				.to(
					images,
					{
						scale: 1,
						opacity: 1,
						duration: 1.2,
						stagger: 0.1,
						ease: "power2.out",
					},
					"-=0.8",
				)
				.to(
					numbers,
					{
						y: 0,
						opacity: 1,
						duration: 0.8,
						stagger: 0.1,
						ease: "back.out(1.7)",
					},
					"-=1",
				)
				.to(
					lines,
					{
						scaleX: 1,
						duration: 0.6,
						stagger: 0.1,
						ease: "power2.out",
					},
					"-=0.6",
				);

			// Parallax effect for images
			images.forEach((image) => {
				gsap.to(image, {
					yPercent: -15,
					ease: "none",
					scrollTrigger: {
						trigger: image,
						start: "top bottom",
						end: "bottom top",
						scrub: 1,
					},
				});
			});

			// Hover animations
			cards.forEach((card, index) => {
				const image = card.querySelector(".benefit-image") as HTMLElement;
				const number = card.querySelector(".benefit-number") as HTMLElement;
				const line = card.querySelector(".benefit-line") as HTMLElement;

				const handleMouseEnter = () => {
					gsap.to(image, {
						scale: 1.05,
						duration: 0.6,
						ease: "power2.out",
					});
					gsap.to(number, {
						scale: 1.1,
						color: "#FF4D45",
						duration: 0.3,
						ease: "power2.out",
					});
					gsap.to(line, {
						scaleX: 1.1,
						duration: 0.4,
						ease: "power2.out",
					});
				};

				const handleMouseLeave = () => {
					gsap.to(image, {
						scale: 1,
						duration: 0.6,
						ease: "power2.out",
					});
					gsap.to(number, {
						scale: 1,
						color: "#171717",
						duration: 0.3,
						ease: "power2.out",
					});
					gsap.to(line, {
						scaleX: 1,
						duration: 0.4,
						ease: "power2.out",
					});
				};

				card.addEventListener("mouseenter", handleMouseEnter);
				card.addEventListener("mouseleave", handleMouseLeave);
			});
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>

				{/* Benefits grid */}
				<div ref={containerRef} className="space-y-24">
					{benefits.map((benefit, index) => {
						const isEven = index % 2 === 0;

						return (
							<div
								key={benefit.id}
								className="benefit-card"
								onMouseEnter={() => setActiveIndex(index)}
								onMouseLeave={() => setActiveIndex(null)}
							>
								<div className="puka-grid items-center gap-8 lg:gap-16">
									{/* Content side */}
									<div
										className={`col-span-12 lg:col-span-6 ${!isEven ? "lg:order-2" : ""}`}
									>
										<div className="space-y-6">
											{/* Number and subtitle */}
											<div className="flex items-center gap-4">
												<span className="benefit-number text-6xl lg:text-7xl font-black text-foreground/20">
													{benefit.number}
												</span>
												<div>
													<div className="benefit-line h-0.5 bg-contrast w-16 mb-2" />
													<span className="text-sm font-bold uppercase tracking-wider text-contrast">
														{benefit.subtitle}
													</span>
												</div>
											</div>

											{/* Title */}
											<Split animationOnScroll>
												<h3 className="text-2xl lg:text-3xl font-bold leading-tight text-foreground">
													{benefit.title}
												</h3>
											</Split>

											{/* Additional content if available */}
											{benefit.content && (
												<Split animationOnScroll delay={0.1}>
													<p className="text-lg text-foreground/70 leading-relaxed">
														{benefit.content}
													</p>
												</Split>
											)}

											{/* Decorative element */}
											<div className="pt-4">
												<div className="w-12 h-12 rounded-full border-2 border-contrast/20 flex items-center justify-center group-hover:border-contrast transition-colors duration-300">
													<div className="w-4 h-4 rounded-full bg-contrast/20 group-hover:bg-contrast transition-colors duration-300" />
												</div>
											</div>
										</div>
									</div>

									{/* Image side */}
									<div
										className={`col-span-12 lg:col-span-6 ${!isEven ? "lg:order-1" : ""}`}
									>
										<div className="relative overflow-hidden rounded-2xl bg-neutral-100 aspect-[4/3]">
											<img
												src={benefit.src}
												alt={benefit.title}
												className="benefit-image w-full h-full object-cover"
											/>

											{/* Overlay gradient */}
											<div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

											{/* Floating number indicator */}
											<div className="absolute top-6 right-6 w-12 h-12 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg">
												<span className="text-sm font-bold text-contrast">
													{benefit.number}
												</span>
											</div>
										</div>
									</div>
								</div>
							</div>
						);
					})}
				</div>

				{/* Bottom CTA */}
				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
