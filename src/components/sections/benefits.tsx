"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const cards = gsap.utils.toArray<HTMLElement>(".benefit-card");
			const images = gsap.utils.toArray<HTMLElement>(".benefit-image");
			const numbers = gsap.utils.toArray<HTMLElement>(".benefit-number");
			const lines = gsap.utils.toArray<HTMLElement>(".benefit-line");

			// Initial setup
			gsap.set(cards, {
				y: 100,
				opacity: 0,
			});

			gsap.set(images, {
				scale: 1.1,
				opacity: 0,
			});

			gsap.set(numbers, {
				y: 50,
				opacity: 0,
			});

			gsap.set(lines, {
				scaleX: 0,
				transformOrigin: "left center",
			});

			// Staggered entrance animation
			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top 70%",
					end: "bottom 30%",
					toggleActions: "play none none reverse",
				},
			});

			tl.to(cards, {
				y: 0,
				opacity: 1,
				duration: 1.2,
				stagger: 0.2,
				ease: "power3.out",
			})
				.to(
					images,
					{
						scale: 1,
						opacity: 1,
						duration: 1,
						stagger: 0.15,
						ease: "power2.out",
					},
					"-=0.8",
				)
				.to(
					numbers,
					{
						y: 0,
						opacity: 1,
						duration: 0.8,
						stagger: 0.1,
						ease: "back.out(1.7)",
					},
					"-=0.6",
				)
				.to(
					lines,
					{
						scaleX: 1,
						duration: 0.6,
						stagger: 0.1,
						ease: "power2.out",
					},
					"-=0.4",
				);

			// Parallax effect for images
			for (const image of images) {
				gsap.to(image, {
					yPercent: -10,
					ease: "none",
					scrollTrigger: {
						trigger: image,
						start: "top bottom",
						end: "bottom top",
						scrub: 1,
					},
				});
			}

			// Sophisticated hover interactions
			for (const [index, card] of cards.entries()) {
				const image = card.querySelector(".benefit-image") as HTMLElement;
				const number = card.querySelector(".benefit-number") as HTMLElement;
				const line = card.querySelector(".benefit-line") as HTMLElement;

				const handleMouseEnter = () => {
					gsap.to(image, {
						scale: 1.05,
						duration: 0.6,
						ease: "power2.out",
					});
					gsap.to(number, {
						scale: 1.1,
						color: "#FF4D45",
						duration: 0.3,
						ease: "power2.out",
					});
					gsap.to(line, {
						scaleX: 1.2,
						duration: 0.4,
						ease: "power2.out",
					});
				};

				const handleMouseLeave = () => {
					gsap.to(image, {
						scale: 1,
						duration: 0.6,
						ease: "power2.out",
					});
					gsap.to(number, {
						scale: 1,
						color: "#171717",
						duration: 0.3,
						ease: "power2.out",
					});
					gsap.to(line, {
						scaleX: 1,
						duration: 0.4,
						ease: "power2.out",
					});
				};

				card.addEventListener("mouseenter", handleMouseEnter);
				card.addEventListener("mouseleave", handleMouseLeave);
			}
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>

				{/* Premium Benefits Showcase */}
				<div ref={containerRef} className="space-y-32">
					{benefits.map((benefit, index) => (
						<div key={benefit.id} className="benefit-card group">
							<div className="puka-grid items-center gap-16">
								{/* Large Number */}
								<div className="col-span-12 lg:col-span-2">
									<div className="relative">
										<span className="benefit-number block text-[12vw] lg:text-[8rem] font-black text-foreground/5 leading-none">
											{benefit.number}
										</span>
										<div className="absolute top-1/2 left-0 transform -translate-y-1/2">
											<div className="benefit-line h-px bg-contrast w-16 mb-4" />
											<span className="text-sm font-bold uppercase tracking-wider text-contrast">
												{benefit.subtitle}
											</span>
										</div>
									</div>
								</div>

								{/* Content */}
								<div className="col-span-12 lg:col-span-6">
									<div className="space-y-8">
										<Split animationOnScroll>
											<h3 className="text-3xl lg:text-4xl font-bold leading-tight text-foreground">
												{benefit.title}
											</h3>
										</Split>

										{benefit.content && (
											<Split animationOnScroll delay={0.1}>
												<p className="text-xl text-foreground/70 leading-relaxed max-w-lg">
													{benefit.content}
												</p>
											</Split>
										)}

										{/* Minimal accent */}
										<div className="w-24 h-px bg-gradient-to-r from-contrast to-transparent" />
									</div>
								</div>

								{/* Image */}
								<div className="col-span-12 lg:col-span-4">
									<div className="relative aspect-[4/5] overflow-hidden bg-neutral-100">
										<img
											src={benefit.src}
											alt={benefit.title}
											className="benefit-image w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
										/>

										{/* Subtle overlay */}
										<div className="absolute inset-0 bg-gradient-to-t from-black/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Bottom CTA */}
				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
