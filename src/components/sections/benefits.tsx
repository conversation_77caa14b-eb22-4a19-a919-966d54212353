"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const morphContainer = containerRef.current.querySelector(
				".morph-container",
			) as HTMLElement;
			const benefitLayers = gsap.utils.toArray<HTMLElement>(".benefit-layer");
			const textElements = gsap.utils.toArray<HTMLElement>(".benefit-text");
			const imageElements = gsap.utils.toArray<HTMLElement>(".benefit-image");

			// Create morphing timeline
			const morphTl = gsap.timeline({
				scrollTrigger: {
					trigger: morphContainer,
					start: "top center",
					end: "bottom center",
					scrub: 1,
					pin: true,
					pinSpacing: true,
				},
			});

			// Initial setup - all layers stacked
			gsap.set(benefitLayers, {
				opacity: 0,
				scale: 0.8,
				rotation: "random(-5, 5)",
			});

			gsap.set(textElements, {
				y: 100,
				opacity: 0,
			});

			gsap.set(imageElements, {
				clipPath: "polygon(50% 50%, 50% 50%, 50% 50%, 50% 50%)",
			});

			// Morphing sequence - each benefit transforms into the next
			benefitLayers.forEach((layer, index) => {
				const isLast = index === benefitLayers.length - 1;
				const nextIndex = isLast ? 0 : index + 1;

				// Show current layer
				morphTl
					.to(
						layer,
						{
							opacity: 1,
							scale: 1,
							rotation: 0,
							duration: 1,
							ease: "power2.out",
						},
						index * 2,
					)
					// Animate text in
					.to(
						layer.querySelector(".benefit-text"),
						{
							y: 0,
							opacity: 1,
							duration: 0.8,
							ease: "power3.out",
						},
						index * 2 + 0.3,
					)
					// Reveal image with clip-path animation
					.to(
						layer.querySelector(".benefit-image"),
						{
							clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
							duration: 1.2,
							ease: "power2.inOut",
						},
						index * 2 + 0.5,
					)
					// Hold for viewing
					.to({}, { duration: 0.8 }, index * 2 + 1.5)
					// Morph out (unless it's the last one)
					.to(
						layer,
						{
							opacity: isLast ? 1 : 0,
							scale: isLast ? 1 : 1.2,
							rotation: isLast ? 0 : "random(-10, 10)",
							duration: isLast ? 0 : 1,
							ease: "power2.in",
						},
						index * 2 + 2.3,
					);
			});

			// Floating particles animation
			const particles = gsap.utils.toArray<HTMLElement>(".particle");
			particles.forEach((particle, index) => {
				gsap.to(particle, {
					x: "random(-100, 100)",
					y: "random(-100, 100)",
					rotation: "random(-180, 180)",
					duration: "random(3, 6)",
					repeat: -1,
					yoyo: true,
					ease: "sine.inOut",
					delay: index * 0.2,
				});
			});

			// Mouse-driven distortion effect
			let mouseX = 0;
			let mouseY = 0;

			const handleMouseMove = (e: MouseEvent) => {
				const rect = morphContainer.getBoundingClientRect();
				mouseX = (e.clientX - rect.left - rect.width / 2) / rect.width;
				mouseY = (e.clientY - rect.top - rect.height / 2) / rect.height;

				gsap.to(morphContainer, {
					rotationY: mouseX * 10,
					rotationX: -mouseY * 10,
					duration: 0.5,
					ease: "power2.out",
				});

				// Distort images based on mouse position
				imageElements.forEach((img, index) => {
					const multiplier = (index + 1) * 0.5;
					gsap.to(img, {
						x: mouseX * 20 * multiplier,
						y: mouseY * 20 * multiplier,
						skewX: mouseX * 5,
						skewY: mouseY * 5,
						duration: 0.3,
						ease: "power2.out",
					});
				});
			};

			const handleMouseLeave = () => {
				gsap.to(morphContainer, {
					rotationY: 0,
					rotationX: 0,
					duration: 1,
					ease: "power2.out",
				});

				gsap.to(imageElements, {
					x: 0,
					y: 0,
					skewX: 0,
					skewY: 0,
					duration: 1,
					ease: "power2.out",
				});
			};

			morphContainer.addEventListener("mousemove", handleMouseMove);
			morphContainer.addEventListener("mouseleave", handleMouseLeave);

			return () => {
				morphContainer.removeEventListener("mousemove", handleMouseMove);
				morphContainer.removeEventListener("mouseleave", handleMouseLeave);
			};
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>

				{/* Revolutionary Morphing Benefits */}
				<div ref={containerRef} className="relative">
					{/* Floating Particles */}
					<div className="absolute inset-0 pointer-events-none">
						{Array.from({ length: 12 }).map((_, i) => (
							<div
								key={`particle-${i}`}
								className="particle absolute w-1 h-1 bg-contrast/30 rounded-full"
								style={{
									left: `${Math.random() * 100}%`,
									top: `${Math.random() * 100}%`,
								}}
							/>
						))}
					</div>

					{/* Morphing Container */}
					<div className="morph-container relative min-h-[200vh] flex items-center justify-center perspective-1000">
						{benefits.map((benefit, index) => (
							<div
								key={benefit.id}
								className="benefit-layer absolute inset-0 flex items-center justify-center"
								style={{ zIndex: benefits.length - index }}
							>
								<div className="puka-grid items-center gap-16 max-w-7xl mx-auto">
									{/* Dynamic Text Side */}
									<div className="col-span-12 lg:col-span-7">
										<div className="benefit-text space-y-8">
											{/* Floating Number */}
											<div className="relative">
												<span className="absolute -top-8 -left-8 text-[20vw] lg:text-[12rem] font-black text-contrast/5 leading-none pointer-events-none">
													{benefit.number}
												</span>
												<div className="relative z-10">
													<div className="inline-block px-4 py-2 bg-contrast/10 text-contrast text-sm font-bold uppercase rounded-full mb-6">
														{benefit.subtitle}
													</div>
													<h3 className="text-4xl lg:text-6xl font-black leading-tight text-foreground mb-8">
														{benefit.title}
													</h3>
													{benefit.content && (
														<p className="text-xl text-foreground/70 leading-relaxed max-w-2xl">
															{benefit.content}
														</p>
													)}
												</div>
											</div>
										</div>
									</div>

									{/* Morphing Image Side */}
									<div className="col-span-12 lg:col-span-5">
										<div className="relative">
											{/* Main Image */}
											<div className="benefit-image relative aspect-[4/5] overflow-hidden bg-gradient-to-br from-neutral-100 to-neutral-200 rounded-3xl">
												<img
													src={benefit.src}
													alt={benefit.title}
													className="w-full h-full object-cover"
												/>

												{/* Morphing Overlay */}
												<div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />

												{/* Floating Elements */}
												<div className="absolute -top-4 -right-4 w-8 h-8 bg-contrast rounded-full flex items-center justify-center shadow-lg">
													<span className="text-white text-sm font-bold">
														{benefit.number}
													</span>
												</div>
											</div>

											{/* Morphing Shapes */}
											<div className="absolute -inset-8 pointer-events-none">
												<div className="absolute top-0 left-0 w-16 h-16 bg-contrast/20 rounded-full blur-xl animate-pulse" />
												<div
													className="absolute bottom-0 right-0 w-24 h-24 bg-contrast/10 rounded-full blur-2xl animate-pulse"
													style={{ animationDelay: "1s" }}
												/>
												<div
													className="absolute top-1/2 -left-4 w-12 h-12 bg-contrast/15 rounded-full blur-lg animate-pulse"
													style={{ animationDelay: "2s" }}
												/>
											</div>
										</div>
									</div>
								</div>
							</div>
						))}
					</div>

					{/* Progress Indicator */}
					<div className="fixed right-8 top-1/2 transform -translate-y-1/2 z-20">
						<div className="flex flex-col gap-3">
							{benefits.map((benefit, index) => (
								<div
									key={`indicator-${benefit.id}`}
									className="w-2 h-8 bg-foreground/20 rounded-full overflow-hidden"
								>
									<div className="w-full h-0 bg-contrast rounded-full transition-all duration-500" />
								</div>
							))}
						</div>
					</div>
				</div>

				{/* Bottom CTA */}
				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
