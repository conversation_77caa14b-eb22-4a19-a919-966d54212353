"use client";

import type { FC } from "react";

const benefits = [

    {
        title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
        src: "Bitmap_2024-02-06-203705_fobz.webp",
    }, 
    {
        title: "Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
        src: "rode.webp",
    },
    {
        title: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²
"
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
    {
        content: "",
        src: "",
    },
]

export const Benefits: FC = () => {
	return (
		<section className="relative">
			<div className=""> </div>
		</section>
	);
};
