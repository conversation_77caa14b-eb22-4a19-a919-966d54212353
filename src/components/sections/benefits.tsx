"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>
                
				<div ref={containerRef} className="space-y-24">
					{/* Benefits in 2x2 Grid */}
					<div className="puka-grid gap-16">
						{benefits.map((benefit) => (
							<div
								key={benefit.id}
								className="benefit-item col-span-12 lg:col-span-6"
							>
								<div className="space-y-8">
									{/* Header */}
									<div className="flex items-start justify-between">
										<div className="benefit-number text-sm font-mono text-foreground/40 tracking-wider">
											{benefit.number}
										</div>
										<div className="text-xs font-mono uppercase tracking-wider text-contrast">
											{benefit.subtitle}
										</div>
									</div>

									{/* Image */}
									<div className="benefit-image relative aspect-[4/3] overflow-hidden bg-neutral-50">
										<img
											src={benefit.src}
											alt={benefit.title}
											className="w-full h-full object-cover"
										/>
									</div>

									{/* Content */}
									<div className="benefit-text space-y-4">
										<h3 className="text-xl lg:text-2xl font-light leading-tight text-foreground">
											{benefit.title}
										</h3>
										{benefit.content && (
											<p className="text-sm text-foreground/60 leading-relaxed font-light">
												{benefit.content}
											</p>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
