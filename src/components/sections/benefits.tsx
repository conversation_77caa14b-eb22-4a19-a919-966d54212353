"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const [activeIndex, setActiveIndex] = useState<number | null>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const centerImage = containerRef.current.querySelector(
				".center-image",
			) as HTMLElement;
			const benefitItems = gsap.utils.toArray<HTMLElement>(".benefit-item");
			const benefitTexts = gsap.utils.toArray<HTMLElement>(".benefit-text");
			const orbits = gsap.utils.toArray<HTMLElement>(".orbit");

			// Initial setup - hide everything
			gsap.set(benefitItems, {
				scale: 0,
				opacity: 0,
				rotation: "random(-180, 180)",
			});

			gsap.set(centerImage, {
				scale: 0,
				rotation: -45,
				opacity: 0,
			});

			gsap.set(benefitTexts, {
				y: 50,
				opacity: 0,
			});

			gsap.set(orbits, {
				scale: 0,
				opacity: 0,
			});

			// Main entrance timeline
			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top 60%",
					end: "bottom 40%",
					toggleActions: "play none none reverse",
				},
			});

			// Animate center image first
			tl.to(centerImage, {
				scale: 1,
				rotation: 0,
				opacity: 1,
				duration: 1.5,
				ease: "back.out(1.7)",
			})
				// Then orbits
				.to(
					orbits,
					{
						scale: 1,
						opacity: 0.1,
						duration: 1,
						stagger: 0.2,
						ease: "power2.out",
					},
					"-=1",
				)
				// Then benefit items in orbital pattern
				.to(
					benefitItems,
					{
						scale: 1,
						opacity: 1,
						rotation: 0,
						duration: 1.2,
						stagger: 0.3,
						ease: "back.out(1.7)",
					},
					"-=0.8",
				)
				// Finally text content
				.to(
					benefitTexts,
					{
						y: 0,
						opacity: 1,
						duration: 0.8,
						stagger: 0.1,
						ease: "power3.out",
					},
					"-=0.6",
				);

			// Continuous orbital rotation
			orbits.forEach((orbit, index) => {
				gsap.to(orbit, {
					rotation: 360,
					duration: 20 + index * 5,
					repeat: -1,
					ease: "none",
				});
			});

			// Floating animation for center image
			gsap.to(centerImage, {
				y: -20,
				duration: 3,
				repeat: -1,
				yoyo: true,
				ease: "sine.inOut",
			});

			// Interactive hover effects
			benefitItems.forEach((item, index) => {
				const image = item.querySelector(".benefit-image") as HTMLElement;
				const content = item.querySelector(".benefit-content") as HTMLElement;

				const handleMouseEnter = () => {
					// Scale up the hovered item
					gsap.to(item, {
						scale: 1.2,
						zIndex: 10,
						duration: 0.5,
						ease: "power2.out",
					});

					// Show content
					gsap.to(content, {
						opacity: 1,
						y: 0,
						duration: 0.3,
						ease: "power2.out",
					});

					// Dim other items
					benefitItems.forEach((otherItem, otherIndex) => {
						if (otherIndex !== index) {
							gsap.to(otherItem, {
								opacity: 0.3,
								scale: 0.9,
								duration: 0.3,
								ease: "power2.out",
							});
						}
					});

					// Pause orbital rotation for hovered item
					const orbit = item.closest(".orbit") as HTMLElement;
					if (orbit) {
						gsap.set(orbit, { animationPlayState: "paused" });
					}
				};

				const handleMouseLeave = () => {
					// Reset all items
					gsap.to(benefitItems, {
						scale: 1,
						opacity: 1,
						zIndex: 1,
						duration: 0.5,
						ease: "power2.out",
					});

					// Hide content
					gsap.to(content, {
						opacity: 0,
						y: 20,
						duration: 0.3,
						ease: "power2.out",
					});

					// Resume orbital rotation
					const orbit = item.closest(".orbit") as HTMLElement;
					if (orbit) {
						gsap.set(orbit, { animationPlayState: "running" });
					}
				};

				item.addEventListener("mouseenter", handleMouseEnter);
				item.addEventListener("mouseleave", handleMouseLeave);
			});

			// Scroll-based image switching in center
			ScrollTrigger.create({
				trigger: containerRef.current,
				start: "top center",
				end: "bottom center",
				scrub: 1,
				onUpdate: (self) => {
					const progress = self.progress;
					const imageIndex = Math.floor(progress * benefits.length);
					const currentImage = centerImage.querySelector(
						"img",
					) as HTMLImageElement;

					if (currentImage && benefits[imageIndex]) {
						const newSrc = benefits[imageIndex].src;
						if (currentImage.src !== newSrc) {
							gsap.to(currentImage, {
								opacity: 0,
								duration: 0.3,
								onComplete: () => {
									currentImage.src = newSrc;
									gsap.to(currentImage, {
										opacity: 1,
										duration: 0.3,
									});
								},
							});
						}
					}
				},
			});
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>

				{/* Revolutionary Orbital Benefits Layout */}
				<div
					ref={containerRef}
					className="relative min-h-[100vh] flex items-center justify-center"
				>
					{/* Central Hub */}
					<div className="center-image relative w-80 h-80 rounded-full overflow-hidden shadow-2xl bg-white">
						<img
							src={benefits[0].src}
							alt="Studio Center"
							className="w-full h-full object-cover"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
						<div className="absolute bottom-6 left-6 right-6 text-center">
							<div className="text-white font-bold text-lg">puka.studio</div>
							<div className="text-white/80 text-sm">Professional Studio</div>
						</div>
					</div>

					{/* Orbital Rings */}
					{[1, 2, 3, 4].map((ring) => (
						<div
							key={ring}
							className="orbit absolute inset-0 rounded-full border border-contrast/10"
							style={{
								width: `${300 + ring * 120}px`,
								height: `${300 + ring * 120}px`,
								margin: "auto",
								top: 0,
								left: 0,
								right: 0,
								bottom: 0,
							}}
						/>
					))}

					{/* Benefit Items in Orbital Positions */}
					{benefits.map((benefit, index) => {
						const angle = index * 90 - 45; // 90 degrees apart, starting at -45
						const radius = 250 + (index % 2) * 80; // Alternating distances
						const x = Math.cos((angle * Math.PI) / 180) * radius;
						const y = Math.sin((angle * Math.PI) / 180) * radius;

						return (
							<div
								key={benefit.id}
								className="benefit-item absolute cursor-pointer"
								style={{
									transform: `translate(${x}px, ${y}px)`,
									left: "50%",
									top: "50%",
									marginLeft: "-80px",
									marginTop: "-80px",
								}}
								onMouseEnter={() => setActiveIndex(index)}
								onMouseLeave={() => setActiveIndex(null)}
							>
								{/* Benefit Circle */}
								<div className="relative w-40 h-40 rounded-full overflow-hidden shadow-xl bg-white border-4 border-white">
									<img
										src={benefit.src}
										alt={benefit.title}
										className="benefit-image w-full h-full object-cover"
									/>

									{/* Number Badge */}
									<div className="absolute -top-2 -right-2 w-8 h-8 bg-contrast rounded-full flex items-center justify-center shadow-lg">
										<span className="text-white text-sm font-bold">
											{benefit.number}
										</span>
									</div>

									{/* Hover Content */}
									<div className="benefit-content absolute inset-0 bg-black/90 flex flex-col items-center justify-center p-4 opacity-0 translate-y-5">
										<div className="text-white text-center">
											<div className="text-xs font-bold uppercase tracking-wider text-contrast mb-2">
												{benefit.subtitle}
											</div>
											<div className="text-sm font-medium leading-tight">
												{benefit.title.length > 60
													? `${benefit.title.substring(0, 60)}...`
													: benefit.title}
											</div>
										</div>
									</div>
								</div>

								{/* Floating Label */}
								<div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-center">
									<div className="text-xs font-bold uppercase tracking-wider text-contrast">
										{benefit.subtitle}
									</div>
								</div>
							</div>
						);
					})}

					{/* Connecting Lines */}
					<svg
						className="absolute inset-0 w-full h-full pointer-events-none"
						style={{ zIndex: -1 }}
					>
						{benefits.map((_, index) => {
							const angle = index * 90 - 45;
							const radius = 250 + (index % 2) * 80;
							const x1 = 50; // Center percentage
							const y1 = 50; // Center percentage
							const x2 = 50 + (Math.cos((angle * Math.PI) / 180) * radius) / 10; // Approximate percentage
							const y2 = 50 + (Math.sin((angle * Math.PI) / 180) * radius) / 10; // Approximate percentage

							return (
								<line
									key={index}
									x1={`${x1}%`}
									y1={`${y1}%`}
									x2={`${x2}%`}
									y2={`${y2}%`}
									stroke="rgba(255, 77, 69, 0.2)"
									strokeWidth="2"
									strokeDasharray="5,5"
								/>
							);
						})}
					</svg>
				</div>

				{/* Benefit Details Panel */}
				<div className="mt-24">
					<div className="puka-grid gap-8">
						{benefits.map((benefit, index) => (
							<div
								key={`detail-${benefit.id}`}
								className={`benefit-text col-span-12 lg:col-span-6 p-8 rounded-2xl bg-white shadow-lg ${
									activeIndex === index ? "ring-2 ring-contrast" : ""
								}`}
							>
								<div className="flex items-start gap-4 mb-4">
									<div className="w-12 h-12 bg-contrast/10 rounded-full flex items-center justify-center flex-shrink-0">
										<span className="text-contrast font-bold">
											{benefit.number}
										</span>
									</div>
									<div>
										<h3 className="text-xl font-bold text-foreground mb-2">
											{benefit.subtitle}
										</h3>
										<p className="text-foreground/70 leading-relaxed">
											{benefit.title}
										</p>
										{benefit.content && (
											<p className="text-foreground/60 text-sm mt-2 leading-relaxed">
												{benefit.content}
											</p>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				</div>

				{/* Bottom CTA */}
				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
