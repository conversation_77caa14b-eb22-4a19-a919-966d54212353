"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const benefits = [
	{
		id: "multi-person",
		title: "Bis zu 4 Personen können gleichzeitig im puka.studio aufnehmen",
		subtitle: "Kollaborative Aufnahmen",
		number: "01",
		src: "/benefits/Bitmap_2024-02-06-203705_fobz.webp",
	},
	{
		id: "microphone-tech",
		title:
			"Für eine authentische Klangkulisse setzen wir auf State-of-the-Art-Mikrofontechnologie, um deine Stimme so naturgetreu wie nur möglich aufzuzeichnen.",
		subtitle: "Premium Audio",
		number: "02",
		src: "/benefits/rode.webp",
	},
	{
		id: "creative-space",
		title: "Kreativität entfesseln in einem vielseitigen Raum",
		content: "Dein Podcast-, Film- & Webinarstudio auf großzügigen 30m²",
		subtitle: "Vielseitiger Raum",
		number: "03",
		src: "/benefits/Group-5-Copy-5.webp",
	},
	{
		id: "cinema-quality",
		title: "Magie in jedem Detail",
		content:
			"Wir setzen auf wegweisende Technologie, indem wir die neuesten Kameras von BlackMagicDesign einsetzen, um deinem Podcast ein cinematisches Qualitätsniveau zu verleihen.",
		subtitle: "Cinema Qualität",
		number: "04",
		src: "/benefits/BLDMicro.webp",
	},
];

export const Benefits: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const items = gsap.utils.toArray<HTMLElement>(".benefit-item");
			const images = gsap.utils.toArray<HTMLElement>(".benefit-image");
			const texts = gsap.utils.toArray<HTMLElement>(".benefit-text");
			const numbers = gsap.utils.toArray<HTMLElement>(".benefit-number");

			// Initial state - everything hidden
			gsap.set(items, {
				opacity: 0,
				y: 60,
			});

			gsap.set(images, {
				scale: 1.1,
				opacity: 0,
			});

			gsap.set(texts, {
				y: 30,
				opacity: 0,
			});

			gsap.set(numbers, {
				opacity: 0,
				scale: 0.8,
			});

			// Entrance animation
			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top 80%",
					end: "bottom 20%",
					toggleActions: "play none none reverse",
				},
			});

			tl.to(items, {
				opacity: 1,
				y: 0,
				duration: 1,
				stagger: 0.15,
				ease: "power3.out",
			})
				.to(
					images,
					{
						scale: 1,
						opacity: 1,
						duration: 1.2,
						stagger: 0.1,
						ease: "power2.out",
					},
					"-=0.8",
				)
				.to(
					texts,
					{
						y: 0,
						opacity: 1,
						duration: 0.8,
						stagger: 0.1,
						ease: "power3.out",
					},
					"-=1",
				)
				.to(
					numbers,
					{
						opacity: 1,
						scale: 1,
						duration: 0.6,
						stagger: 0.1,
						ease: "back.out(1.7)",
					},
					"-=0.6",
				);

			// Subtle parallax for images
			for (const image of images) {
				gsap.to(image, {
					yPercent: -5,
					ease: "none",
					scrollTrigger: {
						trigger: image,
						start: "top bottom",
						end: "bottom top",
						scrub: 1,
					},
				});
			}

			// Refined hover interactions
			for (const item of items) {
				const image = item.querySelector(".benefit-image") as HTMLElement;
				const text = item.querySelector(".benefit-text") as HTMLElement;

				const handleMouseEnter = () => {
					gsap.to(image, {
						scale: 1.02,
						duration: 0.8,
						ease: "power2.out",
					});
					gsap.to(text, {
						y: -5,
						duration: 0.6,
						ease: "power2.out",
					});
				};

				const handleMouseLeave = () => {
					gsap.to(image, {
						scale: 1,
						duration: 0.8,
						ease: "power2.out",
					});
					gsap.to(text, {
						y: 0,
						duration: 0.6,
						ease: "power2.out",
					});
				};

				item.addEventListener("mouseenter", handleMouseEnter);
				item.addEventListener("mouseleave", handleMouseLeave);
			}
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background elements */}
			<div className="absolute inset-0 bg-gradient-to-b from-transparent via-neutral-50/30 to-transparent" />

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-24">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Warum
							<br />
							<span className="text-contrast">puka.studio</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Entdecke die Vorteile unseres professionellen Studios
						</p>
					</Split>
				</div>

				{/* Clean Benefits Grid - Rejouice Style */}
				<div ref={containerRef} className="space-y-40">
					{benefits.map((benefit, index) => (
						<div key={benefit.id} className="benefit-item">
							<div className="puka-grid items-start gap-16">
								{/* Minimal Number */}
								<div className="col-span-12 lg:col-span-1">
									<div className="benefit-number text-sm font-mono text-foreground/40 tracking-wider">
										{benefit.number}
									</div>
								</div>

								{/* Clean Content */}
								<div className="col-span-12 lg:col-span-6">
									<div className="benefit-text space-y-6">
										<div className="text-xs font-mono uppercase tracking-wider text-contrast">
											{benefit.subtitle}
										</div>
										<h3 className="text-3xl lg:text-4xl font-light leading-tight text-foreground">
											{benefit.title}
										</h3>
										{benefit.content && (
											<p className="text-lg text-foreground/60 leading-relaxed font-light max-w-lg">
												{benefit.content}
											</p>
										)}
									</div>
								</div>

								{/* Refined Image */}
								<div className="col-span-12 lg:col-span-5">
									<div className="benefit-image relative aspect-[5/6] overflow-hidden bg-neutral-50">
										<img
											src={benefit.src}
											alt={benefit.title}
											className="w-full h-full object-cover"
										/>
									</div>
								</div>
							</div>

							{/* Subtle Divider */}
							<div className="mt-40 h-px bg-gradient-to-r from-transparent via-foreground/10 to-transparent" />
						</div>
					))}
				</div>

				{/* Bottom CTA */}
				<div className="text-center mt-24">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Studio buchen
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
