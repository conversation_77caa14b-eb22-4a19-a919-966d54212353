"use client";

import { type FC, useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { use<PERSON>enis } from "lenis/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

type AnimationState = {
	scrollProgress: number;
	initialTranslateY: number;
	currentTranslateY: number;
	movementMultiplier: number;
	scale: number;
	fontSize: number;
	gap: number;
	targetMouseX: number;
	currentMouseX: number;
};

const BREAKPOINTS = [
	{ maxWidth: 1000, translateY: -135, movMultiplier: 450 },
	{ maxWidth: 1100, translateY: -130, movMultiplier: 500 },
	{ maxWidth: 1200, translateY: -125, movMultiplier: 550 },
	{ maxWidth: 1300, translateY: -120, movMultiplier: 600 },
];

function getInitialValues() {
	const width = typeof window !== "undefined" ? window.innerWidth : 0;
	for (const bp of BREAKPOINTS) {
		if (width <= bp.maxWidth) {
			return {
				translateY: bp.translateY,
				movementMultiplier: bp.movMultiplier,
			};
		}
	}
	return { translateY: -105, movementMultiplier: 650 };
}

export const Intro: FC = () => {
	const rootRef = useRef<HTMLElement | null>(null);
	const desktopContainerRef = useRef<HTMLDivElement | null>(null);

	useGSAP(
		() => {
			const isDesktop = () => window.innerWidth >= 900;
			if (!isDesktop()) return;

			const videoContainer = desktopContainerRef.current;
			if (!videoContainer) return;

			const videoTitleElements =
				gsap.utils.toArray<HTMLElement>(".video-title p");

			const initial = getInitialValues();
			const state: AnimationState = {
				scrollProgress: 0,
				initialTranslateY: initial.translateY,
				currentTranslateY: initial.translateY,
				movementMultiplier: initial.movementMultiplier,
				scale: 0.25,
				fontSize: 80,
				gap: 2,
				targetMouseX: 0,
				currentMouseX: 0,
			};

			gsap.set(videoContainer, {
				yPercent: state.initialTranslateY,
				xPercent: 0,
				scale: state.scale,
				willChange: "transform",
			});

			const onResize = () => {
				const nv = getInitialValues();
				state.initialTranslateY = nv.translateY;
				state.movementMultiplier = nv.movementMultiplier;
				if (state.scrollProgress === 0) {
					state.currentTranslateY = nv.translateY;
					gsap.set(videoContainer, { yPercent: nv.translateY });
				}
			};
			window.addEventListener("resize", onResize);

			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: rootRef.current,
					start: "top bottom",
					end: "top 10%",
					scrub: true,
					onUpdate: (self) => {
						state.scrollProgress = self.progress;

						state.currentTranslateY = gsap.utils.interpolate(
							state.initialTranslateY,
							0,
							state.scrollProgress,
						);

						state.scale = gsap.utils.interpolate(0.25, 1, state.scrollProgress);
						state.gap = gsap.utils.interpolate(2, 1, state.scrollProgress);

						if (state.scrollProgress <= 0.4) {
							const first = state.scrollProgress / 0.4;
							state.fontSize = gsap.utils.interpolate(80, 40, first);
						} else {
							const second = (state.scrollProgress - 0.4) / 0.6;
							state.fontSize = gsap.utils.interpolate(40, 20, second);
						}
					},
				},
			});

			const onMouseMove = (e: MouseEvent) => {
				state.targetMouseX = (e.clientX / window.innerWidth - 0.5) * 2;
			};
			document.addEventListener("mousemove", onMouseMove);

			let rafId = 0;
			const loop = () => {
				if (!isDesktop()) return;

				const scaledMovementMultiplier =
					(1 - state.scale) * state.movementMultiplier;
				const maxHorizontal =
					state.scale < 0.95
						? state.targetMouseX * scaledMovementMultiplier
						: 0;

				state.currentMouseX = gsap.utils.interpolate(
					state.currentMouseX,
					maxHorizontal,
					0.05,
				);

				videoContainer.style.transform = `translateY(${state.currentTranslateY}%) translateX(${state.currentMouseX}px) scale(${state.scale})`;
				videoContainer.style.gap = `${state.gap}em`;

				for (const el of videoTitleElements) {
					el.style.fontSize = `${state.fontSize}px`;
				}

				rafId = requestAnimationFrame(loop);
			};
			rafId = requestAnimationFrame(loop);

			return () => {
				cancelAnimationFrame(rafId);
				document.removeEventListener("mousemove", onMouseMove);
				window.removeEventListener("resize", onResize);
				tl?.scrollTrigger?.kill();
				tl?.kill();
				ScrollTrigger.refresh();
			};
		},
		{ scope: rootRef },
	);

	return (
		<>
			<section ref={rootRef} className="intro | h-full">
				<div
					ref={desktopContainerRef}
					className="video-container-desktop | relative translate-y-[-105%] scale-[0.25] will-change-transform hidden lg:block"
				>
					<div className="video-preview | relative w-full aspect-video bg-neutral-50 overflow-hidden">
						<div className="video-wrapper | absolute top-0 left-0 h-full w-full overflow-hidden">
							<video
								autoPlay
								loop
								muted
								className="absolute top-0 left-0 w-full h-full pointer-events-none"
							>
								<source src="/short_clip.mp4" />
								<track kind="captions" default />
							</video>
						</div>
					</div>
				</div>

				<div className="video-container-mobile | lg:hidden w-full max-w-[800px] mx-auto">
					<div className="video-preview | relative w-full aspect-video bg-neutral-50 overflow-hidden">
						<div className="video-wrapper | absolute top-0 left-0 h-full w-full overflow-hidden">
							<video
								autoPlay
								loop
								muted
								className="absolute top-0 left-0 w-full h-full pointer-events-none"
							>
								<source src="/short_clip.mp4" />
								<track kind="captions" default />
							</video>
						</div>
					</div>
				</div>
			</section>
			<section className="puka-layout-block grid place-items-center py-72">
				<Split animationOnScroll>
					<h3 className="text-[3vw] text-center leading-none w-[75vw] font-black uppercase">
						Entfessele die Kraft deiner Stimme in unserem Studio Dein Content,
						unsere Technik, ein zauberhaftes Paar Podcasts waren noch nie so
						einfach aufzunehmen
					</h3>
				</Split>
			</section>
		</>
	);
};
