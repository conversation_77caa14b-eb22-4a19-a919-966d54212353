"use client";

import type { FC } from "react";
import { useRef } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { use<PERSON>enis } from "lenis/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const podcasts = [
	{
		id: "mutpropaganda",
		title: "Mutpropaganda",
		src: "/pods/Mutpropaganda.webp",
	},
	{
		id: "inhouse-council",
		title: "INHOUSE COUNCIL CORNER",
		src: "/pods/<EMAIL>",
	},
	{
		id: "mach-es-gesund",
		title: "MACH ES! gesund | CO-Produktion Videocast",
		src: "/pods/Bitmap_2025-08-15-174120_dhcp.webp",
	},
	{
		id: "wirsindliwest",
		title: "#wirsindliwest - der Podcast rund um die Arbeitswelt bei LIWEST",
		src: "/pods/<EMAIL>",
	},
	{
		id: "leo-podcast",
		title:
			"LEO – dein Podcast für die Technologie- und Start-up-Welt in Oberösterreich",
		src: "/pods/Cover_neu.webp",
	},
	{
		id: "wholesome-meditations",
		title: "Wholesome Meditations",
		src: "/pods/Artboard_Final.webp",
	},
	{
		id: "powerful-me",
		title: "POWERFUL ME | CO-Produktion Videocast",
		src: "/pods/a6600c1c-838e-40ff-a760-5e65981f67d4.jpg",
	},
	{ id: "puka-one", title: "puka.one", src: "/pods/puka_one.webp" },
	{ id: "nachts-wach", title: "Nachts Wach", src: "/pods/Nachts_wach.webp" },
];

export const Podcasts: FC = () => {
	const rootRef = useRef<HTMLElement | null>(null);
	const trackRef = useRef<HTMLDivElement | null>(null);

	// Keep ScrollTrigger in sync with Lenis
	useLenis(ScrollTrigger.update);

	useGSAP(
		() => {
			if (!rootRef.current || !trackRef.current) return;

			const section = rootRef.current;
			const track = trackRef.current;

			// reset any inline transforms on refresh so width is correct
			const ctx = gsap.context(() => {
				gsap.set(track, { clearProps: "x" });

				const getMaxShift = () =>
					Math.max(0, track.scrollWidth - window.innerWidth);

				// Move LEFT -> RIGHT (content slides right as you scroll)
				// Start fully shifted left (negative X), end at 0
				gsap.fromTo(
					track,
					{ x: 0 },
					{
						x: () => -getMaxShift(),
						ease: "none",
						scrollTrigger: {
							trigger: section,
							start: "top top",
							end: () => `+=${getMaxShift()}`, // scrub length based on content width
							scrub: true,
							pin: true,
							anticipatePin: 1,
							invalidateOnRefresh: true,
						},
					},
				);
			}, rootRef);

			// cleanup
			return () => ctx.revert();
		},
		{ scope: rootRef },
	);

	return (
		<section className="relative py-32 overflow-hidden" ref={rootRef}>
			<div className="puka-layout-grid relative z-10">
				<div className="mb-20 flex justify-between items-end col-span-9 col-start-4">
					<div className="space-y-6">
						<Split animationOnScroll>
							<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%]">
								Unsere
								<br />
								<span className="text-contrast">Podcasts</span>
							</h2>
						</Split>
						<Split animationOnScroll delay={0.2}>
							<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl">
								Höre inspirierende Podcasts, mit Liebe produziert
							</p>
						</Split>
					</div>
					<div className="text-center mt-20">
						<Split animationOnScroll delay={0.4}>
							<button
								type="button"
								className="variant--default text-xl uppercase font-bold"
							>
								Alle Podcasts entdecken
							</button>
						</Split>
					</div>
				</div>
			</div>

			{/* Horizontal track */}
			<div className="overflow-hidden">
				<div
					ref={trackRef}
					className="flex items-center gap-x-6 will-change-transform"
				>
					<div className="flex-shrink-0 basis-1/4 aspect-[3/4]" />

					{podcasts.map((podcast) => (
						<div
							key={podcast.id}
							className="flex-shrink-0 basis-1/4 aspect-[3/4] bg-neutral-100 rounded-md grid place-items-center"
						>
							<img
								src={podcast.src}
								alt={podcast.title}
								className="aspect-square w-1/2 object-contain"
								loading="lazy"
							/>
						</div>
					))}
					<div className="flex-shrink-0 basis-1/4 aspect-[3/4]" />
					<div className="flex-shrink-0 basis-1/4 aspect-[3/4]" />
				</div>
			</div>
		</section>
	);
};
