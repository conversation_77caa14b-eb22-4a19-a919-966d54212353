"use client";

import type { FC } from "react";
import { useRef, useState } from "react";
import gsap from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";
import { useGSAP } from "@gsap/react";
import { use<PERSON>enis } from "lenis/react";
import { Split } from "../motion/split";

gsap.registerPlugin(ScrollTrigger);

const podcasts = [
	{
		id: "mutpropaganda",
		title: "Mutpropaganda",
		src: "/pods/Mutpropaganda.webp",
	},
	{
		id: "inhouse-council",
		title: "INHOUSE COUNCIL CORNER",
		src: "/pods/<EMAIL>",
	},
	{
		id: "mach-es-gesund",
		title: "MACH ES! gesund | CO-Produktion Videocast",
		src: "/pods/Bitmap_2025-08-15-174120_dhcp.webp",
	},
	{
		id: "wirsindliwest",
		title: "#wirsindliwest - der Podcast rund um die Arbeitswelt bei LIWEST",
		src: "/pods/<EMAIL>",
	},
	{
		id: "leo-podcast",
		title:
			"LEO – dein Podcast für die Technologie- und Start-up-Welt in Oberösterreich",
		src: "/pods/Cover_neu.webp",
	},
	{
		id: "wholesome-meditations",
		title: "Wholesome Meditations",
		src: "/pods/Artboard_Final.webp",
	},
	{
		id: "powerful-me",
		title: "POWERFUL ME | CO-Produktion Videocast",
		src: "/pods/a6600c1c-838e-40ff-a760-5e65981f67d4.jpg",
	},
	{ id: "puka-one", title: "puka.one", src: "/pods/puka_one.webp" },
	{ id: "nachts-wach", title: "Nachts Wach", src: "/pods/Nachts_wach.webp" },
];

export const Podcasts: FC = () => {
	const sectionRef = useRef<HTMLElement>(null);
	const containerRef = useRef<HTMLDivElement>(null);
	const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);

	useGSAP(
		() => {
			if (!containerRef.current) return;

			const cards = gsap.utils.toArray<HTMLElement>(".podcast-card");
			const images = gsap.utils.toArray<HTMLElement>(".podcast-image");
			const titles = gsap.utils.toArray<HTMLElement>(".podcast-title");

			// Initial setup
			gsap.set(cards, {
				y: 100,
				opacity: 0,
				scale: 0.8,
				rotationX: 15,
			});

			// Staggered entrance animation
			const tl = gsap.timeline({
				scrollTrigger: {
					trigger: containerRef.current,
					start: "top 80%",
					end: "bottom 20%",
					toggleActions: "play none none reverse",
				},
			});

			tl.to(cards, {
				y: 0,
				opacity: 1,
				scale: 1,
				rotationX: 0,
				duration: 1.2,
				stagger: {
					amount: 0.8,
					from: "random",
				},
				ease: "power3.out",
			});

			// Floating animation for cards
			cards.forEach((card, index) => {
				gsap.to(card, {
					y: "random(-20, 20)",
					rotation: "random(-2, 2)",
					duration: "random(3, 5)",
					repeat: -1,
					yoyo: true,
					ease: "sine.inOut",
					delay: index * 0.2,
				});
			});

			// Parallax effect for images
			for (const image of images) {
				gsap.to(image, {
					yPercent: -20,
					ease: "none",
					scrollTrigger: {
						trigger: image,
						start: "top bottom",
						end: "bottom top",
						scrub: true,
					},
				});
			}

			// Magnetic effect on hover
			for (const [index, card] of cards.entries()) {
				const handleMouseMove = (e: MouseEvent) => {
					const rect = card.getBoundingClientRect();
					const centerX = rect.left + rect.width / 2;
					const centerY = rect.top + rect.height / 2;
					const deltaX = (e.clientX - centerX) * 0.1;
					const deltaY = (e.clientY - centerY) * 0.1;

					gsap.to(card, {
						x: deltaX,
						y: deltaY,
						rotation: deltaX * 0.1,
						duration: 0.3,
						ease: "power2.out",
					});
				};

				const handleMouseLeave = () => {
					gsap.to(card, {
						x: 0,
						y: 0,
						rotation: 0,
						duration: 0.5,
						ease: "power2.out",
					});
				};

				card.addEventListener("mousemove", handleMouseMove);
				card.addEventListener("mouseleave", handleMouseLeave);
			}
		},
		{ scope: sectionRef },
	);

	return (
		<section ref={sectionRef} className="relative py-32 overflow-hidden">
			{/* Background gradient */}
			<div className="absolute inset-0 bg-gradient-to-br from-orange-50 via-blue-50 to-purple-50 opacity-30" />

			{/* Floating elements */}
			<div className="absolute inset-0 pointer-events-none">
				{Array.from({ length: 20 }).map((_, i) => (
					<div
						key={`floating-${i}`}
						className="absolute w-2 h-2 bg-contrast/20 rounded-full animate-pulse"
						style={{
							left: `${Math.random() * 100}%`,
							top: `${Math.random() * 100}%`,
							animationDelay: `${Math.random() * 3}s`,
							animationDuration: `${2 + Math.random() * 2}s`,
						}}
					/>
				))}
			</div>

			<div className="puka-layout-block relative z-10">
				{/* Section header */}
				<div className="text-center mb-20">
					<Split animationOnScroll>
						<h2 className="text-[8vw] lg:text-[6vw] font-black uppercase leading-[80%] mb-6">
							Unsere
							<br />
							<span className="text-contrast">Podcasts</span>
						</h2>
					</Split>
					<Split animationOnScroll delay={0.2}>
						<p className="text-lg lg:text-xl text-foreground/70 max-w-2xl mx-auto">
							Höre inspirierende Podcasts, mit Liebe produziert
						</p>
					</Split>
				</div>

				{/* Podcast grid */}
				<div
					ref={containerRef}
					className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-12"
				>
					{podcasts.map((podcast, index) => (
						<div
							key={podcast.id}
							className="podcast-card group cursor-pointer"
							onMouseEnter={() => setHoveredIndex(index)}
							onMouseLeave={() => setHoveredIndex(null)}
						>
							<div className="relative overflow-hidden rounded-2xl bg-white shadow-lg hover:shadow-2xl transition-shadow duration-500">
								{/* Image container */}
								<div className="relative aspect-square overflow-hidden">
									<img
										src={podcast.src}
										alt={podcast.title}
										className="podcast-image w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
									/>

									{/* Overlay */}
									<div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

									{/* Play button */}
									<div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-50 group-hover:scale-100">
										<div className="w-16 h-16 bg-white/90 backdrop-blur-sm rounded-full flex items-center justify-center shadow-lg">
											<div className="w-0 h-0 border-l-[12px] border-l-contrast border-y-[8px] border-y-transparent ml-1" />
										</div>
									</div>
								</div>

								{/* Content */}
								<div className="p-6">
									<h3 className="podcast-title text-lg font-bold leading-tight text-foreground group-hover:text-contrast transition-colors duration-300">
										{podcast.title}
									</h3>

									{/* Animated underline */}
									<div className="mt-4 h-0.5 bg-contrast/20 rounded-full overflow-hidden">
										<div
											className="h-full bg-contrast transition-transform duration-500 origin-left"
											style={{
												transform:
													hoveredIndex === index ? "scaleX(1)" : "scaleX(0)",
											}}
										/>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* Call to action */}
				<div className="text-center mt-20">
					<Split animationOnScroll delay={0.4}>
						<button
							type="button"
							className="variant--default text-xl uppercase font-bold"
						>
							Alle Podcasts entdecken
						</button>
					</Split>
				</div>
			</div>
		</section>
	);
};
